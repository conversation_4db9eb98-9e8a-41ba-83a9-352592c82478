import { useEffect, useState } from "react";
import styled from "styled-components";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import Container from "../../../components/Container";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";

import copyIconHover from "../../../assets/images/profile/copyIconHover.svg";
import copyIcon from "../../../assets/images/profile/copyIconNormal.svg";
import editIconHover from "../../../assets/images/profile/editHover.svg";
import editIcon from "../../../assets/images/profile/editNormal.svg";
import ApproveEditCardHoverIcon from "../../../components/ApproveEdit/ApproveEditCardHoverIcon";
import ViewEditCardHoverIcon from "../../../components/ApproveEdit/ViewEditCardHoverIcon";
import { Genders } from "../../../constants/enum";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { BankAccountPurpose } from "../../../models/DTOs/bankAccount/BankAccountDTO";
import { ApproveEmployeeEditDTO } from "../../../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeeEditDTO } from "../../../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import { DeleteEmployeeEditsDTO } from "../../../models/DTOs/editEmployee/DeleteEmployeeEditsDTO";
import {
  approveEmployeeEdit,
  declineEmployeeEdit,
  deleteEmployeeEdits,
} from "../../../services/employees/employeesService";
import Translator, { translate } from "../../../services/language/Translator";
import { deepEqual } from "../../../utils/compareUtils";
import { useMenu } from "../../MenuContext";

const FieldsetRow = styled(Container)`
  display: grid;
  grid-template-columns: 17% 25% 13% 30%;
  gap: 2rem;
  margin: 0.5rem 1rem;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 1rem;
`;

const EmployeeName = styled(Label)`
  text-align: center;
  font-weight: bold;
  font-size: 1.4rem;
  word-wrap: normal;
  margin-top: 0.5rem;
`;

const DepartmentInfo = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1rem;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 2rem;
`;

const DepartmentName = styled(Label)`
  font-size: 1.15rem;
  margin-top: 0.2rem;
  color: var(--profile-department-name-font-color);
`;

const DepartmentLeader = styled(Label)`
  font-size: 1.05rem;
  color: var(--profile-department-leader-name-font-color);
  margin-top: 0.1rem;
`;

const ContractInfo = styled(Container)`
  display: flex;
  text-align: center;
  position: relative;
  top: 4rem;
  flex-direction: column;
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 12rem;
  padding: 1rem;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 9rem;
    margin-left: -2.5rem;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  flex: 1;

  @media (max-width: 1300px) {
    flex-direction: column;
    margin-left: 0;
    width: 100%;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 1rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
  }

  @media (max-width: 1000px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
`;
const EditButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isDisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background: no-repeat center / 1.6rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;

  &:hover {
    background: no-repeat center / 1.6rem url(${(p) => p.hoverImage});
  }
`;

const ButtonsContainer = styled(Container)`
  display: flex;
`;

const EditNameButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isDisabled?: boolean;
}>`
  width: 2.5rem;
  height: 2.5rem;
  background: no-repeat center / 1.8rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;
  background-color: transparent;

  &:hover {
    background: no-repeat center / 1.8rem url(${(p) => p.hoverImage});
  }
`;

interface Props {
  incomingProfile: PersonalInformationDTO;
  employeeName: string;
  isApprover: boolean;
  shouldShowEditButtons: boolean;
}

const PersonalData = ({
  incomingProfile,
  employeeName,
  isApprover,
  shouldShowEditButtons,
}: Props) => {
  const [profile, setProfile] = useState(incomingProfile);
  const [prevoiusIban, setPreviousIban] = useState<string | null>(null);
  const [previousName, setPreviousName] = useState<string>("");
  const [identityCardPreviousAddress, setIdentityCardPreviousAddress] =
    useState<AddressDTO | undefined>(undefined);
  const [identityCardAddress, setIdentityCardAddress] =
    useState<AddressDTO | null>();
  const { toggleMenu, changeView } = useMenu();

  function formatFullAddress(address?: AddressDTO | null) {
    if (!address) return "";
    const parts = [];
    if (address.neighborhood) parts.push(`${address.neighborhood},`);
    if (address.street) parts.push(` ${address.street}`);
    if (address.block) parts.push(`, ${address.block}`);
    if (address.apartment) parts.push(`, ${address.apartment}`);
    return parts.join(" ");
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, ".");
  };

  const handleEdit = (step: number) => {
    changeView("edit-employee", "other", { incomingProfile, step });
    toggleMenu();
  };

  const handleCopyPersonName = async () => {
    try {
      await navigator.clipboard.writeText(employeeName);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  const checkAndResetPreviousValues = () => {
    if (
      profile.employeePreviousValues &&
      deepEqual(profile.employee, profile.employeePreviousValues)
    ) {
      deleteEmployeeEdits(
        new DeleteEmployeeEditsDTO({
          employeeId: profile.employee.workTimeId,
        })
      );

      setProfile({
        ...profile,
        employeePreviousValues: null,
      });
    }
  };

  useEffect(() => {
    setProfile(incomingProfile);
  }, [incomingProfile]);

  useEffect(() => {
    setIdentityCardAddress(
      incomingProfile?.addresses?.find(
        (address) =>
          address.purpose &&
          address.purpose.identifier === AddressPurpose.IdentityCard
      ) || null
    );
  }, [incomingProfile]);

  useEffect(() => {
    setPreviousIban(
      profile?.employeePreviousValues?.bankAccounts?.find(
        (account) => account.purpose.identifier === BankAccountPurpose.Salary
      )?.iban ?? null
    );
  }, [profile]);

  useEffect(() => {
    setIdentityCardPreviousAddress(
      profile?.employeePreviousValues?.addresses?.find(
        (address) =>
          address.purpose &&
          address.purpose.identifier === AddressPurpose.IdentityCard
      )
    );
  }, [profile]);

  useEffect(() => {
    let name = "";

    if (profile.employeePreviousValues?.firstName) {
      name += profile.employeePreviousValues?.firstName;
    }
    if (profile.employeePreviousValues?.secondName) {
      name += " " + profile.employeePreviousValues?.secondName;
    }
    if (profile.employeePreviousValues?.lastName) {
      name += " " + profile.employeePreviousValues?.lastName;
    }

    setPreviousName(name);
  }, [profile]);

  return (
    <WrapperContainer>
      <LeftContainer data-testid="profile-left-container">
        <EmployeeImage src={profileMan} data-testid="profile-employee-image" />
        <EmployeeName data-testid="profile-employee-name">
          {shouldShowEditButtons && isApprover && previousName !== employeeName
            ? previousName!
            : employeeName}
        </EmployeeName>
        {shouldShowEditButtons &&
          previousName !== employeeName &&
          (isApprover ? (
            <ApproveEditCardHoverIcon
              newValue={employeeName}
              onConfirm={async () => {
                await approveEmployeeEdit(
                  new ApproveEmployeeEditDTO({
                    employeeId: profile.employee.workTimeId,
                    propertyName: "FirstName",
                  })
                );
                await approveEmployeeEdit(
                  new ApproveEmployeeEditDTO({
                    employeeId: profile.employee.workTimeId,
                    propertyName: "SecondName",
                  })
                );
                await approveEmployeeEdit(
                  new ApproveEmployeeEditDTO({
                    employeeId: profile.employee.workTimeId,
                    propertyName: "LastName",
                  })
                );
                setProfile({
                  ...profile,
                  employeePreviousValues: profile.employeePreviousValues
                    ? {
                        ...profile.employeePreviousValues,
                        firstName: profile.employee.firstName,
                        secondName: profile.employee.secondName,
                        lastName: profile.employee.lastName,
                      }
                    : null,
                });
                checkAndResetPreviousValues();
              }}
              onCancel={async () => {
                await declineEmployeeEdit(
                  new DeclineEmployeeEditDTO({
                    employeeId: profile.employee.workTimeId,
                    propertyName: "FirstName",
                  })
                );
                await declineEmployeeEdit(
                  new DeclineEmployeeEditDTO({
                    employeeId: profile.employee.workTimeId,
                    propertyName: "SecondName",
                  })
                );
                await declineEmployeeEdit(
                  new DeclineEmployeeEditDTO({
                    employeeId: profile.employee.workTimeId,
                    propertyName: "LastName",
                  })
                );
                setProfile({
                  ...profile,
                  employee: {
                    ...profile.employee,
                    firstName: profile.employeePreviousValues!.firstName,
                    secondName: profile.employeePreviousValues!.secondName,
                    lastName: profile.employeePreviousValues!.lastName,
                  },
                });
                checkAndResetPreviousValues();
              }}
            />
          ) : (
            <ViewEditCardHoverIcon previousValue={previousName} />
          ))}
        <ButtonsContainer>
          <EditNameButton
            data-testid="copy-name-button"
            normalImage={copyIcon}
            hoverImage={copyIconHover}
            onClick={handleCopyPersonName}
            label=""
            isDisabled={true}
          />
          <EditNameButton
            data-testid="edit-company-button"
            normalImage={editIcon}
            hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            isDisabled={true}
          />
        </ButtonsContainer>
        <DepartmentInfo data-testid="profile-department-info">
          <DepartmentName data-testid="profile-department-name">
            Department Name
          </DepartmentName>
          <DepartmentLeader data-testid="profile-department-leader">
            Manager
          </DepartmentLeader>
        </DepartmentInfo>
        <ContractInfo data-testid="profile-contract-info"></ContractInfo>
      </LeftContainer>
      <RightContainer data-testid="profile-right-container">
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="personal-information-fieldset"
        >
          <EditButton
            data-testid="edit-company-button"
            normalImage={editIcon}
            hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            isDisabled={true}
          />
          <Legend data-testid="information-legend">Personal Information</Legend>
          <FieldsetRow data-testid="first-row">
            <LabelColumn>
              <LightLabel>EGN / LNCH</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                isApprover &&
                profile?.employeePreviousValues?.egn !== profile?.employee?.egn
                  ? profile?.employeePreviousValues?.egn
                  : profile?.employee?.egn}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.egn !==
                  profile?.employee?.egn &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.employee?.egn}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "EGN",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              egn: profile.employee.egn,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "EGN",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          egn: profile.employeePreviousValues!.egn,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={profile?.employeePreviousValues?.egn}
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>IBAN</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                isApprover &&
                prevoiusIban !== profile?.iban
                  ? prevoiusIban
                  : profile?.iban}
              </ValueLabel>
              {shouldShowEditButtons &&
                prevoiusIban !== profile?.iban &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.iban}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IBAN",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              bankAccounts:
                                profile.employeePreviousValues.bankAccounts?.map(
                                  (account) =>
                                    account.purpose.identifier ===
                                    BankAccountPurpose.Salary
                                      ? {
                                          ...account,
                                          iban: profile.iban || account.iban,
                                        }
                                      : account
                                ),
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "BankAccounts",
                          collectionItemId: profile.employee.bankAccounts.find(
                            (account) =>
                              account.purpose.identifier ===
                              BankAccountPurpose.Salary
                          )?.id,
                          propertyName: "IBAN",
                        })
                      );
                      const previousIban =
                        profile.employeePreviousValues?.bankAccounts?.find(
                          (account) =>
                            account.purpose.identifier ===
                            BankAccountPurpose.Salary
                        )?.iban;
                      setProfile({
                        ...profile,
                        iban: previousIban!,
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon previousValue={prevoiusIban ?? ""} />
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="second-row">
            <LabelColumn>
              <LightLabel>Date of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                isApprover &&
                profile?.employeePreviousValues?.birthDate !==
                  profile?.employee?.birthDate
                  ? formatDate(profile?.employeePreviousValues?.birthDate)
                  : formatDate(profile?.employee?.birthDate)}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.birthDate !==
                  profile?.employee?.birthDate &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={formatDate(profile?.employee?.birthDate)}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthDate",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              birthDate: profile.employee.birthDate,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthDate",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          birthDate: profile.employeePreviousValues!.birthDate,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={formatDate(
                      profile?.employeePreviousValues?.birthDate
                    )}
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>
                {profile?.employee?.email || profile?.employee?.email !== ""
                  ? "E-mail"
                  : "User number"}
              </LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employee?.email || profile?.employee?.email !== ""
                  ? profile?.employee?.email
                  : profile?.code}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="third-row">
            <LabelColumn>
              <LightLabel>Place of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                profile?.employeePreviousValues?.birthPlace !==
                  profile?.employee?.birthPlace &&
                isApprover
                  ? profile?.employeePreviousValues?.birthPlace
                  : profile?.employee?.birthPlace}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.birthPlace !==
                  profile?.employee?.birthPlace &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.employee?.birthPlace}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthPlace",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              birthPlace: profile.employee.birthPlace,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthPlace",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          birthPlace:
                            profile.employeePreviousValues!.birthPlace,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={profile?.employeePreviousValues?.birthPlace}
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Phone number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                profile?.employeePreviousValues?.phone !==
                  profile?.employee?.phone &&
                isApprover
                  ? profile?.employeePreviousValues?.phone
                  : profile?.employee?.phone}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.phone !==
                  profile?.employee?.phone &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.employee?.phone}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Phone",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              phone: profile.employee.phone,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Phone",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          phone: profile.employeePreviousValues!.phone,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={profile?.employeePreviousValues?.phone}
                  />
                ))}
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="address-fieldset"
        >
          <Legend data-testid="address-legend">ID card details</Legend>
          <FieldsetRow data-testid="address-row">
            <EditButton
              data-testid="edit-company-button"
              normalImage={editIcon}
              hoverImage={editIconHover}
              onClick={() => handleEdit(2)}
              label=""
              isDisabled={true}
            />
            <LabelColumn>
              <LightLabel>Number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                profile?.employeePreviousValues?.idNumber !==
                  profile?.employee?.idNumber &&
                isApprover
                  ? profile?.employeePreviousValues?.idNumber
                  : profile?.employee?.idNumber}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.idNumber !==
                  profile?.employee?.idNumber &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.employeePreviousValues?.idNumber}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDNumber",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              idNumber: profile.employee.idNumber,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDNumber",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          idNumber: profile.employeePreviousValues!.idNumber,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={profile?.employeePreviousValues?.idNumber}
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>City</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                (identityCardAddress?.city?.name !==
                  identityCardPreviousAddress?.city?.name ||
                  identityCardAddress?.cityName !==
                    identityCardPreviousAddress?.cityName) &&
                isApprover
                  ? identityCardPreviousAddress?.city?.name ??
                    identityCardPreviousAddress?.cityName
                  : identityCardAddress?.city?.name ??
                    identityCardAddress?.cityName}
              </ValueLabel>
              {shouldShowEditButtons &&
                (identityCardAddress?.city?.name !==
                  identityCardPreviousAddress?.city?.name ||
                  identityCardAddress?.cityName !==
                    identityCardPreviousAddress?.cityName) &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={
                      identityCardAddress?.city?.name ??
                      identityCardAddress?.cityName
                    }
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityId",
                        })
                      );
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityName",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              addresses:
                                profile.employeePreviousValues.addresses?.map(
                                  (addr) =>
                                    addr.purpose?.identifier ===
                                    AddressPurpose.IdentityCard
                                      ? {
                                          ...addr,
                                          city:
                                            identityCardAddress?.city ,
                                          cityName:
                                            identityCardAddress?.cityName ?? "",
                                        }
                                      : addr
                                ),
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityId",
                        })
                      );
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityName",
                        })
                      );
                      setProfile({
                        ...profile,
                        addresses: profile.addresses.map((addr) =>
                          addr.purpose?.identifier ===
                          AddressPurpose.IdentityCard
                            ? {
                                ...addr,
                                city: identityCardPreviousAddress!.city,
                                cityName: identityCardPreviousAddress!.cityName,
                              }
                            : addr
                        ),
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={
                      identityCardPreviousAddress?.city?.name ??
                      identityCardPreviousAddress?.cityName
                    }
                  />
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued on</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                profile?.employeePreviousValues?.idIssueDate !==
                  profile?.employee?.idIssueDate &&
                isApprover
                  ? formatDate(profile?.employeePreviousValues?.idIssueDate)
                  : formatDate(profile?.employee?.idIssueDate)}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.idIssueDate !==
                  profile?.employee?.idIssueDate &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={formatDate(profile?.employee?.idIssueDate)}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssueDate",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              idIssueDate: profile.employee.idIssueDate,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssueDate",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          idIssueDate:
                            profile.employeePreviousValues!.idIssueDate,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={formatDate(
                      profile?.employeePreviousValues?.idIssueDate
                    )}
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>District</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                (identityCardAddress?.district?.name !==
                  identityCardPreviousAddress?.district?.name ||
                  identityCardAddress?.districtName !==
                    identityCardPreviousAddress?.districtName) &&
                isApprover
                  ? identityCardPreviousAddress?.district?.name ??
                    identityCardPreviousAddress?.districtName
                  : identityCardAddress?.district?.name ??
                    identityCardAddress?.districtName}
              </ValueLabel>
              {shouldShowEditButtons &&
                (identityCardAddress?.district?.name !==
                  identityCardPreviousAddress?.district?.name ||
                  identityCardAddress?.districtName !==
                    identityCardPreviousAddress?.districtName) &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={
                      identityCardAddress?.district?.name ??
                      identityCardAddress?.districtName
                    }
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "DistrictId",
                        })
                      );
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "DistrictName",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              addresses:
                                profile.employeePreviousValues.addresses?.map(
                                  (addr) =>
                                    addr.purpose?.identifier ===
                                    AddressPurpose.IdentityCard
                                      ? {
                                          ...addr,
                                          district:
                                            identityCardAddress?.district ??
                                            null,
                                          districtName:
                                            identityCardAddress?.districtName ??
                                            "",
                                        }
                                      : addr
                                ),
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "DistrictId",
                        })
                      );
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "DistrictName",
                        })
                      );
                      setProfile({
                        ...profile,
                        addresses: profile.addresses.map((addr) =>
                          addr.purpose?.identifier ===
                          AddressPurpose.IdentityCard
                            ? {
                                ...addr,
                                district: identityCardPreviousAddress!.district,
                                districtName:
                                  identityCardPreviousAddress!.districtName,
                              }
                            : addr
                        ),
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={
                      identityCardPreviousAddress?.district?.name ??
                      identityCardPreviousAddress?.districtName
                    }
                  />
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued from</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                isApprover &&
                profile?.employeePreviousValues?.idIssuedFrom !==
                  profile?.employee?.idIssuedFrom ? (
                  <ValueLabel>
                    {profile?.employeePreviousValues?.idIssuedFrom ? (
                      <>
                        <Translator getString="MOI" />{" "}
                        {profile.employee.idIssuedFrom}
                      </>
                    ) : null}
                  </ValueLabel>
                ) : (
                  <ValueLabel>
                    {profile?.employee?.idIssuedFrom ? (
                      <>
                        <Translator getString="MOI" />{" "}
                        {profile.employee.idIssuedFrom}
                      </>
                    ) : null}
                  </ValueLabel>
                )}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.idIssuedFrom !==
                  profile?.employee?.idIssuedFrom &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.employee?.idIssuedFrom}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssuedFrom",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              idIssuedFrom: profile.employee.idIssuedFrom,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssuedFrom",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          idIssuedFrom:
                            profile.employeePreviousValues!.idIssuedFrom,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={
                      profile?.employeePreviousValues?.idIssuedFrom
                    }
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Municipality</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                (identityCardAddress?.municipality?.name !==
                  identityCardPreviousAddress?.municipality?.name ||
                  identityCardAddress?.municipalityName !==
                    identityCardPreviousAddress?.municipalityName) &&
                isApprover
                  ? identityCardPreviousAddress?.municipality?.name ??
                    identityCardPreviousAddress?.municipalityName
                  : identityCardAddress?.municipality?.name ??
                    identityCardAddress?.municipalityName}
              </ValueLabel>
              {shouldShowEditButtons &&
                (identityCardAddress?.municipality?.name !==
                  identityCardPreviousAddress?.municipality?.name ||
                  identityCardAddress?.municipalityName !==
                    identityCardPreviousAddress?.municipalityName) &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={
                      identityCardAddress?.municipality?.name ??
                      identityCardAddress?.municipalityName
                    }
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "MunicipalityId",
                        })
                      );
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "MunicipalityName",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              addresses:
                                profile.employeePreviousValues.addresses?.map(
                                  (addr) =>
                                    addr.purpose?.identifier ===
                                    AddressPurpose.IdentityCard
                                      ? {
                                          ...addr,
                                          municipality:
                                            identityCardAddress?.municipality,
                                          municipalityName:
                                            identityCardAddress?.municipalityName,
                                        }
                                      : addr
                                ),
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "MunicipalityId",
                        })
                      );
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "MunicipalityName",
                        })
                      );
                      setProfile({
                        ...profile,
                        addresses: profile.addresses.map((addr) =>
                          addr.purpose?.identifier ===
                          AddressPurpose.IdentityCard
                            ? {
                                ...addr,
                                municipality:
                                  identityCardPreviousAddress!.municipality,
                                municipalityName:
                                  identityCardPreviousAddress!.municipalityName,
                              }
                            : addr
                        ),
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={
                      identityCardPreviousAddress?.municipality?.name ??
                      identityCardPreviousAddress?.municipalityName
                    }
                  />
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Citizenship</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                profile?.employeePreviousValues?.citizenship !==
                  profile?.employee?.citizenship &&
                isApprover
                  ? profile?.employeePreviousValues?.citizenship
                  : profile?.employee?.citizenship}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.citizenship !==
                  profile?.employee?.citizenship &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={profile?.employee?.citizenship}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Citizenship",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              citizenship: profile.employee.citizenship,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Citizenship",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          citizenship:
                            profile.employeePreviousValues!.citizenship,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={profile?.employeePreviousValues?.citizenship}
                  />
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Address</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                formatFullAddress(identityCardPreviousAddress) !==
                  formatFullAddress(identityCardAddress) &&
                isApprover
                  ? formatFullAddress(identityCardPreviousAddress)
                  : formatFullAddress(identityCardAddress)}
              </ValueLabel>
              {shouldShowEditButtons &&
                formatFullAddress(identityCardPreviousAddress) !==
                  formatFullAddress(identityCardAddress) &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={formatFullAddress(identityCardAddress)}
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Street",
                        })
                      );
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Block",
                        })
                      );
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Apartment",
                        })
                      );
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Neighborhood",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              addresses:
                                profile.employeePreviousValues.addresses?.map(
                                  (addr) =>
                                    addr.purpose?.identifier ===
                                    AddressPurpose.IdentityCard
                                      ? {
                                          ...addr,
                                          street: identityCardAddress?.street,
                                          block: identityCardAddress?.block,
                                          apartment:
                                            identityCardAddress?.apartment,
                                          neighborhood:
                                            identityCardAddress?.neighborhood,
                                        }
                                      : addr
                                ),
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Street",
                        })
                      );
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Block",
                        })
                      );
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Apartment",
                        })
                      );
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "Neighborhood",
                        })
                      );
                      setProfile({
                        ...profile,
                        addresses: profile.addresses.map((addr) =>
                          addr.purpose?.identifier ===
                          AddressPurpose.IdentityCard
                            ? {
                                ...addr,
                                street: identityCardPreviousAddress!.street,
                                block: identityCardPreviousAddress!.block,
                                apartment:
                                  identityCardPreviousAddress!.apartment,
                                neighborhood:
                                  identityCardPreviousAddress!.neighborhood,
                              }
                            : addr
                        ),
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={formatFullAddress(
                      identityCardPreviousAddress
                    )}
                  />
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Gender</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {shouldShowEditButtons &&
                profile?.employeePreviousValues?.gender !==
                  profile?.employee?.gender &&
                isApprover ? (
                  profile?.employeePreviousValues?.gender === Genders.Female ? (
                    <Translator getString="Female" />
                  ) : profile?.employeePreviousValues?.gender ===
                    Genders.Male ? (
                    <Translator getString="Male" />
                  ) : (
                    ""
                  )
                ) : profile?.employee?.gender === Genders.Female ? (
                  <Translator getString="Female" />
                ) : profile?.employee?.gender === Genders.Male ? (
                  <Translator getString="Male" />
                ) : (
                  ""
                )}
              </ValueLabel>
              {shouldShowEditButtons &&
                profile?.employeePreviousValues?.gender !==
                  profile?.employee?.gender &&
                (isApprover ? (
                  <ApproveEditCardHoverIcon
                    newValue={
                      profile?.employee?.gender === Genders.Female
                        ? translate("Female")
                        : profile?.employee?.gender === Genders.Male
                        ? translate("Male")
                        : ""
                    }
                    onConfirm={async () => {
                      await approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Gender",
                        })
                      );
                      setProfile({
                        ...profile,
                        employeePreviousValues: profile.employeePreviousValues
                          ? {
                              ...profile.employeePreviousValues,
                              gender: profile.employee.gender,
                            }
                          : null,
                      });
                      checkAndResetPreviousValues();
                    }}
                    onCancel={async () => {
                      await declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Gender",
                        })
                      );
                      setProfile({
                        ...profile,
                        employee: {
                          ...profile.employee,
                          gender: profile.employeePreviousValues!.gender,
                        },
                      });
                      checkAndResetPreviousValues();
                    }}
                  />
                ) : (
                  <ViewEditCardHoverIcon
                    previousValue={
                      profile?.employeePreviousValues?.gender === Genders.Female
                        ? translate("Female")
                        : profile?.employeePreviousValues?.gender ===
                          Genders.Male
                        ? translate("Male")
                        : ""
                    }
                  />
                ))}
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
      </RightContainer>
    </WrapperContainer>
  );
};

export default PersonalData;
